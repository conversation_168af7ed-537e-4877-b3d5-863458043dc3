import type { Metada<PERSON>, Viewport } from "next";
import { Inter } from "next/font/google";
import type { PropsWithChildren } from "react";

import { Footer } from "@/components/main/footer";
import { Navbar } from "@/components/main/navbar";
import { StarsCanvas } from "@/components/main/star-background";
import { BlueCursor } from "@/components/ui/blue-cursor";
import { siteConfig } from "@/config";
import { cn } from "@/lib/utils";

import "./globals.css";

// Add smooth scroll behavior
import "locomotive-scroll/dist/locomotive-scroll.css";

const inter = Inter({ subsets: ["latin"] });

export const viewport: Viewport = {
  themeColor: "#030014",
};

export const metadata: Metadata = siteConfig;

export default function RootLayout({ children }: Readonly<PropsWithChildren>) {
  return (
    <html lang="en">
      <body
        className={cn(
          "bg-[#030014] overflow-x-hidden",
          inter.className
        )}
      >
        <StarsCanvas />
        <BlueCursor />
        <Navbar />
        <div className="smooth-scroll-wrapper">
          {children}
        </div>
        <Footer />
      </body>
    </html>
  );
}
