"use client";

import { motion, useMotionValue, useSpring } from "framer-motion";
import { useEffect, useState } from "react";

export const BlueCursor = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isPointer, setIsPointer] = useState(false);

  // Mouse position values for actual cursor position
  const cursorX = useMotionValue(-100);
  const cursorY = useMotionValue(-100);

  // Apply spring physics for an immediate following effect with no perceptible delay
  // Extremely high stiffness values create virtually no delay
  const mainDotConfig = { damping: 20, stiffness: 900 }; // Immediate following for main dot
  const trailConfig = { damping: 25, stiffness: 800 }; // Very slight delay for trail effect

  // Springs for main cursor dot - will follow cursor immediately
  const cursorXSpring = useSpring(cursorX, mainDotConfig);
  const cursorYSpring = useSpring(cursorY, mainDotConfig);

  // Springs for trailing dot - follows with barely perceptible delay
  const trailXSpring = useSpring(cursorX, trailConfig); // Connect directly to cursor position
  const trailYSpring = useSpring(cursorY, trailConfig); // Connect directly to cursor position

  useEffect(() => {
    // Only run on client
    if (typeof window === "undefined") return;

    const moveCursor = (e: MouseEvent) => {
      cursorX.set(e.clientX);
      cursorY.set(e.clientY);
      setIsVisible(true);
    };

    const handleMouseEnter = () => setIsVisible(true);
    const handleMouseLeave = () => setIsVisible(false);

    // Check if cursor is over clickable elements
    const handleMouseOver = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const isClickable =
        target.tagName.toLowerCase() === "a" ||
        target.tagName.toLowerCase() === "button" ||
        target.closest("a") !== null ||
        target.closest("button") !== null ||
        target.classList.contains("clickable") ||
        target.closest(".clickable") !== null ||
        window.getComputedStyle(target).cursor === "pointer";

      setIsPointer(isClickable);
    };

    window.addEventListener("mousemove", moveCursor);
    window.addEventListener("mouseenter", handleMouseEnter);
    window.addEventListener("mouseleave", handleMouseLeave);
    window.addEventListener("mouseover", handleMouseOver);

    return () => {
      window.removeEventListener("mousemove", moveCursor);
      window.removeEventListener("mouseenter", handleMouseEnter);
      window.removeEventListener("mouseleave", handleMouseLeave);
      window.removeEventListener("mouseover", handleMouseOver);
    };
  }, [cursorX, cursorY]);

  return (
    <>
      {/* Trailing dot (follows with virtually no delay) */}
      <motion.div
        className="fixed top-0 left-0 rounded-full pointer-events-none z-[9998] will-change-transform"
        style={{
          x: trailXSpring,
          y: trailYSpring,
          translateX: "-50%",
          translateY: "-50%",
          width: isPointer ? "18px" : "10px",
          height: isPointer ? "18px" : "10px",
          opacity: isVisible ? 0.35 : 0,
          backgroundColor: "#60A5FA", // Lighter blue for trail
          boxShadow: "0 0 10px rgba(59, 130, 246, 0.4)",
          transition: "width 0.1s, height 0.1s, opacity 0.1s", // Immediate transitions
        }}
      />

      {/* Main blue dot cursor */}
      <motion.div
        className="fixed top-0 left-0 rounded-full pointer-events-none z-[9999] will-change-transform"
        style={{
          x: cursorXSpring,
          y: cursorYSpring,
          translateX: "-50%",
          translateY: "-50%",
          width: isPointer ? "24px" : "12px",
          height: isPointer ? "24px" : "12px",
          opacity: isVisible ? 0.8 : 0,
          backgroundColor: "#3B82F6", // Blue color
          boxShadow: "0 0 12px rgba(59, 130, 246, 0.6)", // Enhanced blue glow
          transition: "width 0.1s, height 0.1s, opacity 0.1s", // Immediate transitions
        }}
      />
    </>
  );
};
